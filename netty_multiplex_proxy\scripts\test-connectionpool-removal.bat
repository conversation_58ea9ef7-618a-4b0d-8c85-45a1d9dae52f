@echo off
echo ========================================
echo ProxyServerV2 ConnectionPool移除验证测试
echo ========================================
echo.

cd /d "%~dp0.."

echo [1/5] 编译项目...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)
echo 编译成功！
echo.

echo [2/5] 运行单元测试...
call mvn test -Dtest=AsyncConnectionTest -q
if %ERRORLEVEL% neq 0 (
    echo 异步连接测试失败！
    pause
    exit /b 1
)
echo 异步连接测试通过！
echo.

echo [3/5] 运行性能测试...
call mvn test -Dtest=AsyncConnectionPerformanceTest -q
if %ERRORLEVEL% neq 0 (
    echo 性能测试失败！
    pause
    exit /b 1
)
echo 性能测试通过！
echo.

echo [4/5] 运行集成测试...
call mvn test -Dtest=ProxyServerV2IntegrationTest -q
if %ERRORLEVEL% neq 0 (
    echo 集成测试失败！
    pause
    exit /b 1
)
echo 集成测试通过！
echo.

echo [5/5] 验证编译无错误...
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo 编译验证失败！
    pause
    exit /b 1
)
echo 编译验证通过！
echo.

echo ========================================
echo 所有测试通过！ConnectionPool移除成功！
echo ========================================
echo.
echo 主要改进：
echo - 完全移除了ConnectionPool相关代码
echo - 使用AsyncTcpDirectOutboundHandler替代传统连接池
echo - 增强了AsyncOutboundConnection的功能
echo - 完善了MultiplexSession的连接管理
echo - 优化了ProxyProcessor的活跃连接管理
echo - 更新了配置文件，移除连接池配置
echo.
echo 核心功能验证：
echo ✓ 异步连接创建和管理
echo ✓ 数据缓存和发送
echo ✓ 连接健康检查和清理
echo ✓ 错误处理和恢复
echo ✓ 性能监控和统计
echo ✓ 多路复用会话管理
echo ✓ 完整的代理流程
echo.

pause
